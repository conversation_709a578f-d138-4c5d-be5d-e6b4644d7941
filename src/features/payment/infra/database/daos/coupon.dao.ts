import { CouponDto } from '@features/payment/domain';
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from 'src/shared';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { CouponRecord } from '@features/payment/infra/database/records';
import { CouponRecordMapper } from '@features/payment/infra/database/record-mappers';
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';

export interface ICouponDao extends IBaseDao<CouponDto, CouponRecord> {
    update(entity: Partial<CouponDto>, options?: RepositoryOptions): Promise<Partial<CouponDto>>;
    findByCode(code: string, options?: RepositoryOptions): Promise<CouponDto | null>;
    findActiveByCode(code: string, options?: RepositoryOptions): Promise<CouponDto | null>;
}

@Dao({
    token: PAYMENT_MODULE_INJECT_TOKENS.DAO.COUPON,
    scope: Lifecycle.Singleton,
})
export class CouponDao extends BaseDao<CouponDto, CouponRecord> implements ICouponDao {
    private readonly logger = new Logger(this.constructor.name);

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: TableNames.COUPON,
            recordMapper: new CouponRecordMapper(),
        });
    }

    async update(dto: Partial<CouponDto>, options: RepositoryOptions = {}): Promise<Partial<CouponDto>> {
        const client = this.db.getClient();
        const record = this.recordMapper.fromDtoToRecord(dto);
        const query = client.table(this.tableName).where('id', dto.id!).update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
        return dto;
    }

    async findByCode(code: string, options: RepositoryOptions = {}): Promise<CouponDto | null> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('code', code).first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;
        return record ? this.recordMapper.fromRecordToDto(record as CouponRecord) : null;
    }

    async findActiveByCode(code: string, options: RepositoryOptions = {}): Promise<CouponDto | null> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('code', code).where('status', 'active').first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;
        return record ? this.recordMapper.fromRecordToDto(record as CouponRecord) : null;
    }
}
