import { Nullable } from '@heronjs/common';
import { CouponStatusEnum, CouponDurationEnum } from '@features/payment/domain/aggregates/coupon/enums';

export type CouponRecord = {
    id: string;
    name: string;
    code: string;
    status: Nullable<CouponStatusEnum>;
    duration: Nullable<CouponDurationEnum>;
    percent_off: Nullable<number>;
    amount_off: Nullable<number>;
    effect_to: Nullable<number>;
    max_redemptions: Nullable<number>;
    currency: Nullable<string>;
    created_at: Date;
    updated_at: Nullable<Date>;
};
