import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import {
    CreateGatewayCustomerInput,
    UpdateGatewayCustomerInput,
} from '@features/payment/domain/aggregates/gateway-customer/types';

export type GatewayCustomerProps = {
    id: string;
    gateway: GatewayCodesEnum;
    userId: string;
    gatewayCustomerId: string;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type GatewayCustomerMethods = {
    createGatewayCustomer(payload: CreateGatewayCustomerInput): Promise<void>;
    updateGatewayCustomer(payload: UpdateGatewayCustomerInput): void;
};

export type IGatewayCustomer = IAggregateRoot<GatewayCustomerProps, GatewayCustomerMethods>;

export class GatewayCustomer
    extends AggregateRoot<GatewayCustomerProps, GatewayCustomerMethods>
    implements IGatewayCustomer
{
    static AGGREGATE_NAME = 'gateway-customer';

    constructor(payload: AggregateRootConstructorPayload<GatewayCustomerProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): GatewayCodesEnum {
        return this.props.gateway;
    }

    get userId(): string {
        return this.props.userId;
    }

    get gatewayCustomerId(): string {
        return this.props.gatewayCustomerId;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    protected setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setGateway(payload?: GatewayCodesEnum): void {
        if (payload !== undefined) this.setProp('gateway', payload);
    }

    private setUserId(payload?: string): void {
        if (payload !== undefined) this.setProp('userId', payload);
    }

    private setGatewayCustomerId(payload?: string): void {
        if (payload !== undefined) this.setProp('gatewayCustomerId', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createGatewayCustomer(payload: CreateGatewayCustomerInput) {
        this.setId(payload.id);
        this.setGateway(payload.gateway);
        this.setUserId(payload.userId);
        this.setGatewayCustomerId(payload.gatewayCustomerId);
        this.setCreatedAt(new Date());
    }

    updateGatewayCustomer(payload: UpdateGatewayCustomerInput): void {
        this.setGatewayCustomerId(payload.gatewayCustomerId);
        this.setUpdatedAt(new Date());
    }
}
