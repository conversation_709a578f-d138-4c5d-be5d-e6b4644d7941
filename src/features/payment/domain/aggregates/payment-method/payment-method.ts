import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { PaymentMethodTypeEnum, PaymentMethodFundingEnum } from '@features/payment/domain/aggregates/payment-method/enums';
import { CreatePaymentMethodInput, UpdatePaymentMethodInput } from '@features/payment/domain/aggregates/payment-method/types';

export type PaymentMethodProps = {
    id: string;
    gateway: GatewayCodesEnum;
    last4: string;
    label: string;
    reference: string;
    isDefault: boolean;
    userId: string;
    type: PaymentMethodTypeEnum;
    expMonth: string;
    expYear: string;
    funding: PaymentMethodFundingEnum;
    tenantId: string;
    archived: boolean;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type PaymentMethodMethods = {
    createPaymentMethod(payload: CreatePaymentMethodInput): Promise<void>;
    updatePaymentMethod(payload: UpdatePaymentMethodInput): void;
    archivePaymentMethod(): void;
    setAsDefault(): void;
};

export type IPaymentMethod = IAggregateRoot<PaymentMethodProps, PaymentMethodMethods>;

export class PaymentMethod extends AggregateRoot<PaymentMethodProps, PaymentMethodMethods> implements IPaymentMethod {
    static AGGREGATE_NAME = 'payment-method';

    constructor(payload: AggregateRootConstructorPayload<PaymentMethodProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): GatewayCodesEnum {
        return this.props.gateway;
    }

    get last4(): string {
        return this.props.last4;
    }

    get label(): string {
        return this.props.label;
    }

    get reference(): string {
        return this.props.reference;
    }

    get isDefault(): boolean {
        return this.props.isDefault;
    }

    get userId(): string {
        return this.props.userId;
    }

    get type(): PaymentMethodTypeEnum {
        return this.props.type;
    }

    get expMonth(): string {
        return this.props.expMonth;
    }

    get expYear(): string {
        return this.props.expYear;
    }

    get funding(): PaymentMethodFundingEnum {
        return this.props.funding;
    }

    get tenantId(): string {
        return this.props.tenantId;
    }

    get archived(): boolean {
        return this.props.archived;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    private setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setGateway(payload?: GatewayCodesEnum): void {
        if (payload !== undefined) this.setProp('gateway', payload);
    }

    private setLast4(payload?: string): void {
        if (payload !== undefined) this.setProp('last4', payload);
    }

    private setLabel(payload?: string): void {
        if (payload !== undefined) this.setProp('label', payload);
    }

    private setReference(payload?: string): void {
        if (payload !== undefined) this.setProp('reference', payload);
    }

    private setIsDefault(payload?: boolean): void {
        if (payload !== undefined) this.setProp('isDefault', payload);
    }

    private setUserId(payload?: string): void {
        if (payload !== undefined) this.setProp('userId', payload);
    }

    private setType(payload?: PaymentMethodTypeEnum): void {
        if (payload !== undefined) this.setProp('type', payload);
    }

    private setExpMonth(payload?: string): void {
        if (payload !== undefined) this.setProp('expMonth', payload);
    }

    private setExpYear(payload?: string): void {
        if (payload !== undefined) this.setProp('expYear', payload);
    }

    private setFunding(payload?: PaymentMethodFundingEnum): void {
        if (payload !== undefined) this.setProp('funding', payload);
    }

    private setTenantId(payload?: string): void {
        if (payload !== undefined) this.setProp('tenantId', payload);
    }

    private setArchived(payload?: boolean): void {
        if (payload !== undefined) this.setProp('archived', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createPaymentMethod(payload: CreatePaymentMethodInput) {
        this.setId(payload.id);
        this.setGateway(payload.gateway);
        this.setLast4(payload.last4);
        this.setLabel(payload.label);
        this.setReference(payload.reference);
        this.setIsDefault(payload.isDefault);
        this.setUserId(payload.userId);
        this.setType(payload.type);
        this.setExpMonth(payload.expMonth);
        this.setExpYear(payload.expYear);
        this.setFunding(payload.funding);
        this.setTenantId(payload.tenantId);
        this.setArchived(payload.archived);
        this.setCreatedAt(new Date());
    }

    updatePaymentMethod(payload: UpdatePaymentMethodInput): void {
        this.setLabel(payload.label);
        this.setIsDefault(payload.isDefault);
        this.setExpMonth(payload.expMonth);
        this.setExpYear(payload.expYear);
        this.setArchived(payload.archived);
        this.setUpdatedAt(new Date());
    }

    archivePaymentMethod(): void {
        this.setArchived(true);
        this.setUpdatedAt(new Date());
    }

    setAsDefault(): void {
        this.setIsDefault(true);
        this.setUpdatedAt(new Date());
    }
}
