import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { CouponStatusEnum, CouponDurationEnum } from '@features/payment/domain/aggregates/coupon/enums';
import { CreateCouponInput, UpdateCouponInput } from '@features/payment/domain/aggregates/coupon/types';

export type CouponProps = {
    id: string;
    name: string;
    code: string;
    status: Nullable<CouponStatusEnum>;
    duration: Nullable<CouponDurationEnum>;
    percentOff: Nullable<number>;
    amountOff: Nullable<number>;
    effectTo: Nullable<number>;
    maxRedemptions: Nullable<number>;
    currency: Nullable<string>;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type CouponMethods = {
    createCoupon(payload: CreateCouponInput): Promise<void>;
    updateCoupon(payload: UpdateCouponInput): void;
    activateCoupon(): void;
    deactivateCoupon(): void;
};

export type ICoupon = IAggregateRoot<CouponProps, CouponMethods>;

export class Coupon extends AggregateRoot<CouponProps, CouponMethods> implements ICoupon {
    static AGGREGATE_NAME = 'coupon';

    constructor(payload: AggregateRootConstructorPayload<CouponProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get name(): string {
        return this.props.name;
    }

    get code(): string {
        return this.props.code;
    }

    get status(): Nullable<CouponStatusEnum> {
        return this.props.status;
    }

    get duration(): Nullable<CouponDurationEnum> {
        return this.props.duration;
    }

    get percentOff(): Nullable<number> {
        return this.props.percentOff;
    }

    get amountOff(): Nullable<number> {
        return this.props.amountOff;
    }

    get effectTo(): Nullable<number> {
        return this.props.effectTo;
    }

    get maxRedemptions(): Nullable<number> {
        return this.props.maxRedemptions;
    }

    get currency(): Nullable<string> {
        return this.props.currency;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    private setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setName(payload?: string): void {
        if (payload !== undefined) this.setProp('name', payload);
    }

    private setCode(payload?: string): void {
        if (payload !== undefined) this.setProp('code', payload);
    }

    private setStatus(payload?: Nullable<CouponStatusEnum>): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setDuration(payload?: Nullable<CouponDurationEnum>): void {
        if (payload !== undefined) this.setProp('duration', payload);
    }

    private setPercentOff(payload?: Nullable<number>): void {
        if (payload !== undefined) this.setProp('percentOff', payload);
    }

    private setAmountOff(payload?: Nullable<number>): void {
        if (payload !== undefined) this.setProp('amountOff', payload);
    }

    private setEffectTo(payload?: Nullable<number>): void {
        if (payload !== undefined) this.setProp('effectTo', payload);
    }

    private setMaxRedemptions(payload?: Nullable<number>): void {
        if (payload !== undefined) this.setProp('maxRedemptions', payload);
    }

    private setCurrency(payload?: Nullable<string>): void {
        if (payload !== undefined) this.setProp('currency', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createCoupon(payload: CreateCouponInput) {
        this.setId(payload.id);
        this.setName(payload.name);
        this.setCode(payload.code);
        this.setStatus(payload.status);
        this.setDuration(payload.duration);
        this.setPercentOff(payload.percentOff);
        this.setAmountOff(payload.amountOff);
        this.setEffectTo(payload.effectTo);
        this.setMaxRedemptions(payload.maxRedemptions);
        this.setCurrency(payload.currency);
        this.setCreatedAt(new Date());
    }

    updateCoupon(payload: UpdateCouponInput): void {
        this.setName(payload.name);
        this.setCode(payload.code);
        this.setStatus(payload.status);
        this.setDuration(payload.duration);
        this.setPercentOff(payload.percentOff);
        this.setAmountOff(payload.amountOff);
        this.setEffectTo(payload.effectTo);
        this.setMaxRedemptions(payload.maxRedemptions);
        this.setCurrency(payload.currency);
        this.setUpdatedAt(new Date());
    }

    activateCoupon(): void {
        this.setStatus(CouponStatusEnum.ACTIVE);
        this.setUpdatedAt(new Date());
    }

    deactivateCoupon(): void {
        this.setStatus(CouponStatusEnum.INACTIVE);
        this.setUpdatedAt(new Date());
    }
}
