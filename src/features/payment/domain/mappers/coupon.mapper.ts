import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { BaseMapper, IMapper } from '@cbidigital/aqua-ddd';
import { CouponDto } from '@features/payment/domain/dtos';
import { Coupon, ICoupon } from '@features/payment/domain/aggregates/coupon/coupon';

export type ICouponMapper = IMapper<CouponDto, ICoupon>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.COUPON,
    scope: Lifecycle.Singleton,
})
export class CouponMapper extends BaseMapper implements ICouponMapper {
    constructor() {
        super();
    }

    async fromEntityToDto(entity: ICoupon): Promise<CouponDto> {
        return {
            id: entity.id,
            name: entity.name,
            code: entity.code,
            status: entity.status,
            duration: entity.duration,
            percentOff: entity.percentOff,
            amountOff: entity.amountOff,
            effectTo: entity.effectTo,
            maxRedemptions: entity.maxRedemptions,
            currency: entity.currency,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }

    async fromDtoToEntity(dto: CouponDto): Promise<ICoupon> {
        return new Coupon({
            id: dto.id,
            props: {
                id: dto.id,
                name: dto.name,
                code: dto.code,
                status: dto.status,
                duration: dto.duration,
                percentOff: dto.percentOff,
                amountOff: dto.amountOff,
                effectTo: dto.effectTo,
                maxRedemptions: dto.maxRedemptions,
                currency: dto.currency,
                createdAt: dto.createdAt,
                updatedAt: dto.updatedAt,
            },
        });
    }
}
