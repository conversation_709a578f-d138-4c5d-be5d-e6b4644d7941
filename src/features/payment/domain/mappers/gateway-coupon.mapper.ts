import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { BaseMapper, IMapper } from '@cbidigital/aqua-ddd';
import { GatewayCouponDto } from '@features/payment/domain/dtos';
import { GatewayCoupon, IGatewayCoupon } from '@features/payment/domain/aggregates/gateway-coupon/gateway-coupon';

export type IGatewayCouponMapper = IMapper<GatewayCouponDto, IGatewayCoupon>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY_COUPON,
    scope: Lifecycle.Singleton,
})
export class GatewayCouponMapper extends BaseMapper implements IGatewayCouponMapper {
    constructor() {
        super();
    }

    async fromEntityToDto(entity: IGatewayCoupon): Promise<GatewayCouponDto> {
        return {
            id: entity.id,
            gateway: entity.gateway,
            couponId: entity.couponId,
            gatewayCouponId: entity.gatewayCouponId,
            status: entity.status,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }

    async fromDtoToEntity(dto: GatewayCouponDto): Promise<IGatewayCoupon> {
        return new GatewayCoupon({
            id: dto.id,
            props: {
                id: dto.id,
                gateway: dto.gateway,
                couponId: dto.couponId,
                gatewayCouponId: dto.gatewayCouponId,
                status: dto.status,
                createdAt: dto.createdAt,
                updatedAt: dto.updatedAt,
            },
        });
    }
}
