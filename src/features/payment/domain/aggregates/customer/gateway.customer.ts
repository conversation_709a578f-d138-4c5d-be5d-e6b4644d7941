import { Nullable } from '@cbidigital/heron-common';
import { BaseEntity, EntityField } from '@cbidigital/aqua/domain';
import { GatewayCustomerCreatePayload, GatewayCustomerUpdatePayload } from '../types';
import { v4 as randomUUID } from 'uuid';
export interface GatewayCustomerProps {
    id: string;
    gatewayCode: string;
    userId: string;
    tenantId: string;
    gatewayCustomerId: string;
    createdAt: Date;
    updatedAt: Nullable<Date>;
}

export interface IGatewayCustomer extends GatewayCustomerProps {
    update(input: GatewayCustomerUpdatePayload): void;
}

export class GatewayCustomer extends BaseEntity implements IGatewayCustomer {
    constructor(payload: GatewayCustomerProps = {} as any) {
        super();
        this._id = payload.id;
        this._gatewayCode = payload.gatewayCode;
        this._userId = payload.userId;
        this._gatewayCustomerId = payload.gatewayCustomerId;
        this._createdAt = payload.createdAt;
        this._updatedAt = payload.updatedAt;
        this._tenantId = payload.tenantId;
    }

    /** Properties **/

    /**
     * id
     */
    private _id: string;

    @EntityField()
    get id(): string {
        return this._id;
    }

    /**
     * gatewayCode
     */
    private _gatewayCode: string;

    @EntityField()
    get gatewayCode(): string {
        return this._gatewayCode;
    }

    /**
     * userId
     */
    private _userId: string;

    @EntityField()
    get userId(): string {
        return this._userId;
    }

    /**
     * userId
     */
    private _tenantId: string;

    @EntityField()
    get tenantId(): string {
        return this._tenantId;
    }
    /**
     * gatewayCustomerId
     */
    private _gatewayCustomerId: string;

    @EntityField()
    get gatewayCustomerId(): string {
        return this._gatewayCustomerId;
    }

    /**
     * createdAt
     */
    private _createdAt: Date;

    @EntityField()
    get createdAt(): Date {
        return this._createdAt;
    }

    /**
     * updatedAt
     */
    private _updatedAt: Nullable<Date>;

    @EntityField()
    get updatedAt(): Nullable<Date> {
        return this._updatedAt;
    }

    /** Methods **/

    private setId(id?: string): void {
        if (id !== undefined) this._id = id;
    }

    private setGatewayCode(gatewayCode?: string): void {
        if (gatewayCode !== undefined) this._gatewayCode = gatewayCode;
    }

    private setTenantId(tenantId?: string): void {
        if (tenantId !== undefined) this._tenantId = tenantId;
    }

    private setUserId(userId?: string): void {
        if (userId !== undefined) this._userId = userId;
    }

    private setGatewayCustomerId(gatewayCustomerId?: string): void {
        if (gatewayCustomerId !== undefined) this._gatewayCustomerId = gatewayCustomerId;
    }

    private setCreatedAt(createdAt?: Date): void {
        if (createdAt !== undefined) this._createdAt = createdAt;
    }

    private setUpdatedAt(updatedAt?: Date): void {
        if (updatedAt !== undefined) this._updatedAt = updatedAt;
    }

    public create(payload: GatewayCustomerCreatePayload): void {
        this.setId(payload.id ?? randomUUID());
        this.setGatewayCode(payload.gatewayCode);
        this.setUserId(payload.userId);
        this.setGatewayCustomerId(payload.gatewayCustomerId);
        this.setTenantId(payload.tenantId);
        this.setCreatedAt(new Date());
    }

    public update(payload: GatewayCustomerUpdatePayload): void {
        this.setGatewayCustomerId(payload.gatewayCustomerId);
        this.setUpdatedAt(new Date());
    }
}
