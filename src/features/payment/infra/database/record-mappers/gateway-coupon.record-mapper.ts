import { IRecordMapper } from '@cbidigital/aqua-ddd';
import { GatewayCouponDto } from '@features/payment/domain';
import { GatewayCouponRecord } from '@features/payment/infra/database/records';

export class GatewayCouponRecordMapper implements IRecordMapper<GatewayCouponDto, GatewayCouponRecord> {
    fromRecordToDto(record: GatewayCouponRecord): GatewayCouponDto {
        return {
            id: record.id,
            gateway: record.gateway,
            couponId: record.coupon_id,
            gatewayCouponId: record.gateway_coupon_id,
            status: record.status,
            createdAt: record.created_at,
            updatedAt: record.updated_at,
        };
    }

    fromDtoToRecord(dto: GatewayCouponDto): GatewayCouponRecord {
        return {
            id: dto.id,
            gateway: dto.gateway,
            coupon_id: dto.couponId,
            gateway_coupon_id: dto.gatewayCouponId,
            status: dto.status,
            created_at: dto.createdAt,
            updated_at: dto.updatedAt,
        };
    }

    fromRecordsToDtos(records: GatewayCouponRecord[]): GatewayCouponDto[] {
        return records.map((record) => this.fromRecordToDto(record));
    }

    fromDtosToRecords(dtos: GatewayCouponDto[]): GatewayCouponRecord[] {
        return dtos.map((dto) => this.fromDtoToRecord(dto));
    }
}
