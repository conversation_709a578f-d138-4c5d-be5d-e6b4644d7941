import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { IGatewayCouponDao } from '@features/payment/infra/database';
import { IGatewayCouponRepository } from '@features/payment/domain/repositories';
import { IGatewayCoupon } from '@features/payment/domain/aggregates/gateway-coupon/gateway-coupon';
import { Inject, Optional, Lifecycle, Repository, DataSource } from '@heronjs/common';
import {
    IDatabase,
    QueryInput,
    BaseRepository,
    QueryInputFindOne,
    RepositoryOptions,
} from '@cbidigital/aqua-ddd';
import { GatewayCouponDto, IGatewayCouponMapper } from '@features/payment/domain';

@Repository({
    token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.GATEWAY_COUPON,
    scope: Lifecycle.Singleton,
})
export class GatewayCouponRepository extends BaseRepository<IGatewayCoupon> implements IGatewayCouponRepository {
    constructor(
        @DataSource() protected readonly db: IDatabase,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY_COUPON)
        private readonly gatewayCouponDao: IGatewayCouponDao,
        @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY_COUPON)
        private readonly mapper: IGatewayCouponMapper,
    ) {
        super({ db });
    }

    async create(entity: IGatewayCoupon, options?: RepositoryOptions): Promise<IGatewayCoupon> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.gatewayCouponDao.create(dto, options);
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async update(entity: IGatewayCoupon, options?: RepositoryOptions): Promise<IGatewayCoupon> {
        const trx = options?.trx ?? (await this.db.startTrx(options));
        const dto = await this.mapper.fromEntityToDto(entity);
        await this.gatewayCouponDao.update(dto, { ...options, trx });
        if (!options?.trx) await trx.commit();

        return entity;
    }

    async delete(entity: IGatewayCoupon, options?: RepositoryOptions): Promise<IGatewayCoupon> {
        await this.withTransaction(async (trx) => {
            await this.gatewayCouponDao.deleteById(entity.id, { ...options, trx });
            return entity;
        }, options);

        return entity;
    }

    async find(input: QueryInput, options?: RepositoryOptions): Promise<IGatewayCoupon[]> {
        const dtos = await this.gatewayCouponDao.find(input, options);
        const entities = await this.mapper.fromDtosToEntities(dtos as GatewayCouponDto[]);
        return entities;
    }

    async findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Optional<IGatewayCoupon>> {
        const dto = await this.gatewayCouponDao.findOne(input, options);
        const entity = dto ? await this.mapper.fromDtoToEntity(dto as GatewayCouponDto) : dto;
        return entity;
    }

    async upsertList(entities: IGatewayCoupon[], options: RepositoryOptions) {
        const dtos = await this.mapper.fromEntitiesToDtos(entities);
        return entities;
    }

    async count(input: Pick<QueryInput, 'filter'>, options?: RepositoryOptions): Promise<number> {
        return this.gatewayCouponDao.count(input, options);
    }
}
