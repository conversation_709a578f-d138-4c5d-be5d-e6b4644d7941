export const PAYMENT_MODULE_INJECT_TOKENS = {
    REPOSITORY: {
        GATEWAY: Symbol('GATEWAY_REPOSITORY').toString(),
    },
    BUILDER: {
        GATEWAY: Symbol('GATEWAY_BUILDER').toString(),
    },
    USECASE: {
        // Gateway
        CREATE_GATEWAY: Symbol('CREATE_GATEWAY_USECASE').toString(),
    },
    DAO: {
        GATEWAY: Symbol('GATEWAY_DAO').toString(),
    },
    MAPPER: {
        GATEWAY: Symbol('GATEWAY_MAPPER').toString(),
    },
    SERVICE: {},
    FACTORY: {
        PAYMENT_GATEWAY: Symbol('PAYMENT_GATEWAY_FACTORY').toString(),
    },
    UTIL: {
        RETRY: Symbol('RETRY_UTIL').toString(),
        DATABASE: Symbol('DATABASE_UTIL').toString(),
    },
};
