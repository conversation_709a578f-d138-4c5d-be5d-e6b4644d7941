import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { GatewayCouponStatusEnum } from '@features/payment/domain/aggregates/gateway-coupon/enums';
import {
    CreateGatewayCouponInput,
    UpdateGatewayCouponInput,
} from '@features/payment/domain/aggregates/gateway-coupon/types';

export type GatewayCouponProps = {
    id: string;
    gateway: string;
    couponId: string;
    gatewayCouponId: string;
    status: GatewayCouponStatusEnum;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type GatewayCouponMethods = {
    createGatewayCoupon(payload: CreateGatewayCouponInput): Promise<void>;
    updateGatewayCoupon(payload: UpdateGatewayCouponInput): void;
    markAsSynced(): void;
    markAsFailed(): void;
};

export type IGatewayCoupon = IAggregateRoot<GatewayCouponProps, GatewayCouponMethods>;

export class GatewayCoupon
    extends AggregateRoot<GatewayCouponProps, GatewayCouponMethods>
    implements IGatewayCoupon
{
    static AGGREGATE_NAME = 'gateway-coupon';

    constructor(payload: AggregateRootConstructorPayload<GatewayCouponProps>) {
        super(payload);
    }

    /** Properties **/

    get id(): string {
        return this.props.id;
    }

    get gateway(): string {
        return this.props.gateway;
    }

    get couponId(): string {
        return this.props.couponId;
    }

    get gatewayCouponId(): string {
        return this.props.gatewayCouponId;
    }

    get status(): GatewayCouponStatusEnum {
        return this.props.status;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    protected setId(payload?: string): void {
        if (payload !== undefined) this.setProp('id', payload);
    }

    private setGateway(payload?: string): void {
        if (payload !== undefined) this.setProp('gateway', payload);
    }

    private setCouponId(payload?: string): void {
        if (payload !== undefined) this.setProp('couponId', payload);
    }

    private setGatewayCouponId(payload?: string): void {
        if (payload !== undefined) this.setProp('gatewayCouponId', payload);
    }

    private setStatus(payload?: GatewayCouponStatusEnum): void {
        if (payload !== undefined) this.setProp('status', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createGatewayCoupon(payload: CreateGatewayCouponInput) {
        this.setId(payload.id);
        this.setGateway(payload.gateway);
        this.setCouponId(payload.couponId);
        this.setGatewayCouponId(payload.gatewayCouponId);
        this.setStatus(payload.status);
        this.setCreatedAt(new Date());
    }

    updateGatewayCoupon(payload: UpdateGatewayCouponInput): void {
        this.setGatewayCouponId(payload.gatewayCouponId);
        this.setStatus(payload.status);
        this.setUpdatedAt(new Date());
    }

    markAsSynced(): void {
        this.setStatus(GatewayCouponStatusEnum.SYNCED);
        this.setUpdatedAt(new Date());
    }

    markAsFailed(): void {
        this.setStatus(GatewayCouponStatusEnum.FAILED);
        this.setUpdatedAt(new Date());
    }
}
