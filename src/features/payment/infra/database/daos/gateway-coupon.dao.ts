import { GatewayCouponDto } from '@features/payment/domain';
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from 'src/shared';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { GatewayCouponRecord } from '@features/payment/infra/database/records';
import { GatewayCouponRecordMapper } from '@features/payment/infra/database/record-mappers';
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';

export interface IGatewayCouponDao extends IBaseDao<GatewayCouponDto, GatewayCouponRecord> {
    update(entity: Partial<GatewayCouponDto>, options?: RepositoryOptions): Promise<Partial<GatewayCouponDto>>;
    findByGateway(gateway: string, options?: RepositoryOptions): Promise<GatewayCouponDto[]>;
    findByCouponId(couponId: string, options?: RepositoryOptions): Promise<GatewayCouponDto[]>;
    findByGatewayAndCouponId(gateway: string, couponId: string, options?: RepositoryOptions): Promise<GatewayCouponDto | null>;
}

@Dao({
    token: PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY_COUPON,
    scope: Lifecycle.Singleton,
})
export class GatewayCouponDao extends BaseDao<GatewayCouponDto, GatewayCouponRecord> implements IGatewayCouponDao {
    private readonly logger = new Logger(this.constructor.name);

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: TableNames.GATEWAY_COUPON,
            recordMapper: new GatewayCouponRecordMapper(),
        });
    }

    async update(dto: Partial<GatewayCouponDto>, options: RepositoryOptions = {}): Promise<Partial<GatewayCouponDto>> {
        const client = this.db.getClient();
        const record = this.recordMapper.fromDtoToRecord(dto);
        const query = client.table(this.tableName).where('id', dto.id!).update(record);
        if (options.trx) query.transacting(options.trx);
        await query;
        return dto;
    }

    async findByGateway(gateway: string, options: RepositoryOptions = {}): Promise<GatewayCouponDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('gateway', gateway);
        if (options.trx) query.transacting(options.trx);
        const records = await query;
        return this.recordMapper.fromRecordsToDtos(records);
    }

    async findByCouponId(couponId: string, options: RepositoryOptions = {}): Promise<GatewayCouponDto[]> {
        const client = this.db.getClient();
        const query = client.table(this.tableName).where('coupon_id', couponId);
        if (options.trx) query.transacting(options.trx);
        const records = await query;
        return this.recordMapper.fromRecordsToDtos(records);
    }

    async findByGatewayAndCouponId(gateway: string, couponId: string, options: RepositoryOptions = {}): Promise<GatewayCouponDto | null> {
        const client = this.db.getClient();
        const query = client.table(this.tableName)
            .where('gateway', gateway)
            .where('coupon_id', couponId)
            .first();
        if (options.trx) query.transacting(options.trx);
        const record = await query;
        return record ? this.recordMapper.fromRecordToDto(record) : null;
    }
}
