import { <PERSON><PERSON>ecordMapper } from '@cbidigital/aqua-ddd';
import { CouponDto } from '@features/payment/domain';
import { CouponRecord } from '@features/payment/infra/database/records';

export class CouponRecordMapper implements IRecordMapper<CouponDto, CouponRecord> {
    fromRecordToDto(record: CouponRecord): CouponDto {
        return {
            id: record.id,
            name: record.name,
            code: record.code,
            status: record.status,
            duration: record.duration,
            percentOff: record.percent_off,
            amountOff: record.amount_off,
            effectTo: record.effect_to,
            maxRedemptions: record.max_redemptions,
            currency: record.currency,
            createdAt: record.created_at,
            updatedAt: record.updated_at,
        };
    }

    fromDtoToRecord(dto: CouponDto): CouponRecord {
        return {
            id: dto.id,
            name: dto.name,
            code: dto.code,
            status: dto.status,
            duration: dto.duration,
            percent_off: dto.percentOff,
            amount_off: dto.amountOff,
            effect_to: dto.effectTo,
            max_redemptions: dto.maxRedemptions,
            currency: dto.currency,
            created_at: dto.createdAt,
            updated_at: dto.updatedAt,
        };
    }

    fromRecordsToDtos(records: CouponRecord[]): CouponDto[] {
        return records.map((record) => this.fromRecordToDto(record));
    }

    fromDtosToRecords(dtos: CouponDto[]): CouponRecord[] {
        return dtos.map((dto) => this.fromDtoToRecord(dto));
    }
}
