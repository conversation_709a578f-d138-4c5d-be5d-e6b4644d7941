import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { PaymentMethodTypeEnum, PaymentMethodFundingEnum } from '@features/payment/domain/aggregates/payment-method/enums';

export type CreatePaymentMethodInput = {
    id: string;
    gateway: GatewayCodesEnum;
    last4: string;
    label: string;
    reference: string;
    isDefault: boolean;
    userId: string;
    type: PaymentMethodTypeEnum;
    expMonth: string;
    expYear: string;
    funding: PaymentMethodFundingEnum;
    tenantId: string;
    archived: boolean;
};

export type UpdatePaymentMethodInput = {
    label?: string;
    isDefault?: boolean;
    expMonth?: string;
    expYear?: string;
    archived?: boolean;
};
