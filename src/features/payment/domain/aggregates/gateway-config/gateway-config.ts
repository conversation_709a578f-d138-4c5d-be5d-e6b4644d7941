import { Nullable } from '@heronjs/common';
import { AggregateRoot, AggregateRootConstructorPayload, IAggregateRoot } from '@cbidigital/aqua-ddd';
import { GatewayCodesEnum } from '@features/payment/domain/aggregates/gateway/enums';
import { CreateGatewayConfigInput, UpdateGatewayConfigInput } from '@features/payment/domain/aggregates/gateway-config/types';

export type GatewayConfigProps = {
    code: string;
    gatewayCode: GatewayCodesEnum;
    label: string;
    desc: Nullable<string>;
    value: string;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type GatewayConfigMethods = {
    createGatewayConfig(payload: CreateGatewayConfigInput): Promise<void>;
    updateGatewayConfig(payload: UpdateGatewayConfigInput): void;
};

export type IGatewayConfig = IAggregateRoot<GatewayConfigProps, GatewayConfigMethods>;

export class GatewayConfig extends AggregateRoot<GatewayConfigProps, GatewayConfigMethods> implements IGatewayConfig {
    static AGGREGATE_NAME = 'gateway-config';

    constructor(payload: AggregateRootConstructorPayload<GatewayConfigProps>) {
        super(payload);
    }

    /** Properties **/

    get code(): string {
        return this.props.code;
    }

    get gatewayCode(): GatewayCodesEnum {
        return this.props.gatewayCode;
    }

    get label(): string {
        return this.props.label;
    }

    get desc(): Nullable<string> {
        return this.props.desc;
    }

    get value(): string {
        return this.props.value;
    }

    get createdAt(): Date {
        return this.props.createdAt;
    }

    get updatedAt(): Nullable<Date> {
        return this.props.updatedAt;
    }

    /** Methods **/

    private setCode(payload?: string): void {
        if (payload !== undefined) this.setProp('code', payload);
    }

    private setGatewayCode(payload?: GatewayCodesEnum): void {
        if (payload !== undefined) this.setProp('gatewayCode', payload);
    }

    private setLabel(payload?: string): void {
        if (payload !== undefined) this.setProp('label', payload);
    }

    private setDesc(payload?: Nullable<string>): void {
        if (payload !== undefined) this.setProp('desc', payload);
    }

    private setValue(payload?: string): void {
        if (payload !== undefined) this.setProp('value', payload);
    }

    private setCreatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('createdAt', payload);
    }

    private setUpdatedAt(payload?: Date): void {
        if (payload !== undefined) this.setProp('updatedAt', payload);
    }

    async createGatewayConfig(payload: CreateGatewayConfigInput) {
        this.setCode(payload.code);
        this.setGatewayCode(payload.gatewayCode);
        this.setLabel(payload.label);
        this.setDesc(payload.desc);
        this.setValue(payload.value);
        this.setCreatedAt(new Date());
    }

    updateGatewayConfig(payload: UpdateGatewayConfigInput): void {
        this.setLabel(payload.label);
        this.setDesc(payload.desc);
        this.setValue(payload.value);
        this.setUpdatedAt(new Date());
    }
}
