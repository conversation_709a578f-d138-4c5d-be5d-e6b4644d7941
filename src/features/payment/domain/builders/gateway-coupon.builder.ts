import {
    AggregateRootBuilder,
    IAggregateRootBuilder,
    AggregateRootBuilderPayload,
} from '@cbidigital/aqua-ddd';
import { Lifecycle, Provider } from '@heronjs/common';
import { PAYMENT_MODULE_INJECT_TOKENS } from 'src/shared';
import { GatewayCoupon, IGatewayCoupon } from '@features/payment/domain/aggregates/gateway-coupon/gateway-coupon';

export type GatewayCouponBuilderBuildPayload = AggregateRootBuilderPayload<IGatewayCoupon>;
export type IGatewayCouponBuilder = IAggregateRootBuilder<IGatewayCoupon>;

@Provider({
    token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.GATEWAY_COUPON,
    scope: Lifecycle.Singleton,
})
export class GatewayCouponBuilder extends AggregateRootBuilder<IGatewayCoupon> implements IGatewayCouponBuilder {
    async build({ id, props, externalProps }: GatewayCouponBuilderBuildPayload = {}): Promise<IGatewayCoupon> {
        return new GatewayCoupon({ id, props, externalProps });
    }
}
